设计一个 paas 平台, 先完成上层架构的设计:

- 实现应用管理层, 通过定制一套应用规范, 为后续的具体实现奠定基础. 然后实现具体的 nodejs, python 等应用
- 实现 ci/cd 层, 通过定制一套 ci/cd 规范, 为后续的具体实现奠定基础.
  - 实现 gitea 作为代码仓库
  - 实现 docker 作为构建环境
  - 其他 ci/cd 具体的功能实现
- 实现数据存储层, 通过定制一套数据存储规范, 为后续的具体实现奠定基础. 需要兼容多种数据库, 例如 sqlite, pgsql 等
- 其他功能的顶层设计分析和具体实现的建议

请为我设计并实现一个完整的 PaaS（Platform as a Service）平台架构。请按照以下具体要求进行分层设计和实现：

## 1. 应用管理层设计与实现
- 设计一套标准化的应用规范文档，包括：
  - 应用配置文件格式（如 app.yaml 或 paas.json）
  - 应用生命周期管理规范（部署、启动、停止、更新、回滚）
  - 资源限制和环境变量配置规范
  - 健康检查和监控接口规范
- 实现应用管理核心模块，支持：
  - Node.js 应用的部署和管理
  - Python 应用的部署和管理
  - 应用实例的动态扩缩容
  - 应用版本管理和回滚机制
- 提供 RESTful API 接口用于应用管理操作

## 2. CI/CD 层设计与实现
- 设计 CI/CD 流水线规范，包括：
  - 构建配置文件格式（如 .paas-ci.yaml）
  - 构建阶段定义（代码检出、依赖安装、测试、构建、部署）
  - 环境变量和密钥管理规范
- 集成和配置以下组件：
  - **Gitea**: 作为代码仓库，配置 webhook 触发构建
  - **Docker**: 作为容器化构建和运行环境
  - **构建调度器**: 管理构建任务队列和执行
- 实现具体功能：
  - 自动化构建流水线
  - 多环境部署（开发、测试、生产）
  - 构建日志和状态管理
  - 部署策略（蓝绿部署、滚动更新）

## 3. 数据存储层设计与实现
- 设计数据存储抽象层，包括：
  - 数据库连接池管理
  - ORM/查询构建器抽象接口
  - 数据迁移和版本管理机制
- 实现多数据库支持：
  - SQLite（用于开发和轻量级部署）
  - PostgreSQL（用于生产环境）
  - 数据库适配器模式，便于扩展其他数据库
- 提供数据备份和恢复功能

## 4. 其他核心功能设计
请分析并提供以下功能的顶层设计和实现建议：
- **用户认证与权限管理**: RBAC 权限模型，支持多租户
- **监控与日志系统**: 应用性能监控、日志聚合和分析
- **负载均衡与服务发现**: 内置负载均衡器和服务注册发现
- **配置管理**: 集中化配置管理和热更新
- **安全机制**: 网络隔离、密钥管理、安全扫描

## 实现要求
- 使用 Go 或 Python 作为主要开发语言
- 采用微服务架构，各模块可独立部署
- 提供完整的 API 文档和使用说明
- 包含单元测试和集成测试
- 使用 Docker 容器化部署
- 提供 Web 管理界面（可选）

请先从整体架构设计开始，然后逐步实现各个模块，并为每个模块提供详细的中文文档和代码注释。

---